# Product Requirements Document: "Ha<PERSON>z" Memorization Assistant (Prototype)

### 1. Overview

* **Product Name:** Hafiz (working title)
* **Project Goal:** To create a Flutter-based mobile application that acts as a digital partner for memorizing Arabic text. The app listens to the user recite a predefined text and reveals it word-by-word upon correct pronunciation.
* **Target Audience:** Arabic speakers who need to memorize texts (e.g., students, individuals memorizing religious texts, actors).
* **Core Problem:** The user often lacks a dedicated person to listen to them and correct them during memorization practice. This app provides immediate, private, and repeatable feedback.

### 2. User Stories

* **As a user, I want to** paste text into the app **so that** I can practice memorizing it.
* **As a user, I want to** save my pasted texts **so that** I can easily practice them again later without re-pasting.
* **As a user, I want to** start a memorization session from a saved text **so that** the app can listen to me.
* **As a user, I want to** see placeholders for the text I need to recite **so that** I know the length of the text.
* **As a user, I want to** have the app listen to me speak **so that** it can verify if I'm saying the correct word.
* **As a user, I want to** see the next word appear on the screen when I say it correctly **so that** I get positive reinforcement and can proceed.
* **As a user, I want to** hear a distinct sound and see a visual cue if I say the wrong word **so that** I know I've made a mistake and can try again.
* **As a user, I want to** see a congratulatory message upon completing the text **so that** I feel a sense of accomplishment.

### 3. Functional Requirements

#### 3.1. Text Management

* **FR-1: Add New Text:** The user must be able to paste a block of Arabic text into a text field in the app.
* **FR-2: Save Text:** After pasting, the user must have an option to save the text. Each saved text should be stored locally on the device.
* **FR-3: Text Library:** The app must have a screen that lists all previously saved texts, allowing the user to select one to start a session.

#### 3.2. Memorization Session

* **FR-4: Session Initialization:**
    * Upon selecting a text, the user is taken to the "Session Screen".
    * The screen shall be blank except for a series of placeholders (`...`), one for each word in the selected text. For a 4-word text, the screen would show: `... ... ... ...`.
    * A "Start" button will be present to initiate the microphone and listening process.
* **FR-5: Core Interaction Loop:**
    * Once the session starts, the app will continuously listen to the user's voice.
    * The app will process audio in real-time to transcribe spoken words.
    * **Correct Word:** If the transcribed word matches the next expected word in the text, the corresponding `...` placeholder is replaced with the actual Arabic word. The app then immediately sets its target to the *next* word in sequence.
        > *Example:* If the screen is `... ... ... ...` and the user correctly says the first word, the screen updates to: `بسم ... ... ...`. The app now expects the second word.
    * **Incorrect Word:** If the transcribed word does *not* match the next expected word:
        1.  The app will play a clear "wrong" sound effect.
        2.  The `...` placeholder for the word the user is currently attempting to say will flash red briefly.
        3.  The app will discard the incorrect attempt and continue listening for the correct word.
* **FR-6: Strict Matching:** The comparison between the transcribed word and the expected word must be exact. No normalization of diacritics (*tashkeel*) or word forms will be performed. "الرَّحْمَٰنِ" and "الرحمن" are considered different words.
* **FR-7: Handling Pauses:** If the user pauses speaking, the app will do nothing and simply continue to listen and wait for input. There will be no timeout or automatic hints.
* **FR-8: Session Completion:** Upon correctly reciting the final word of the text, the app will display a "Congratulations!" (or similar) message to the user.

### 4. Non-Functional Requirements

* **NFR-1: Platform:** The application will be developed using the Flutter framework.
* **NFR-2: AI Model:** Transcription will be powered by a Whisper AI model running **locally** on the device to ensure offline functionality and privacy.
* **NFR-3: Performance:**
    * The transcription feedback loop (from user speaking to UI update/feedback) must feel near-instantaneous to the user.
    * To handle rapid speech, the audio processing system should be architected to begin capturing the next audio chunk while the previous one is still being processed.
* **NFR-4: Data Persistence:** User-saved texts must be stored in local device storage (e.g., using SQLite or a similar local database).
* **NFR-5: Language Support:** The primary focus for transcription and text handling is Arabic.

### 5. Out of Scope (For this Prototype)

* User accounts and cloud synchronization.
* Advanced performance analytics (e.g., words per minute, number of mistakes per session).
* Multiple difficulty levels or "leniency" modes for word matching.
* Importing texts from files (e.g., `.txt`, `.pdf`).
* In-app text editing.
* Hints or "show word" functionality.