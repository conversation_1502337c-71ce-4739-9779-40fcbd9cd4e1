import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/saved_text.dart';
import '../../../../core/constants/storage_keys.dart';

part 'saved_text_model.freezed.dart';
part 'saved_text_model.g.dart';

@freezed
class SavedTextModel with _$SavedTextModel {
  const factory SavedTextModel({
    required String id,
    required String title,
    required String content,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _SavedTextModel;
  
  const SavedTextModel._();
  
  factory SavedTextModel.fromJson(Map<String, dynamic> json) =>
      _$SavedTextModelFromJson(json);
  
  /// Creates a SavedTextModel from a database map
  factory SavedTextModel.fromMap(Map<String, dynamic> map) {
    return SavedTextModel(
      id: map[StorageKeys.savedTextId] as String,
      title: map[StorageKeys.savedTextTitle] as String,
      content: map[StorageKeys.savedTextContent] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        map[StorageKeys.savedTextCreatedAt] as int,
      ),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(
        map[StorageKeys.savedTextUpdatedAt] as int,
      ),
    );
  }
  
  /// Converts the model to a database map
  Map<String, dynamic> toMap() {
    return {
      StorageKeys.savedTextId: id,
      StorageKeys.savedTextTitle: title,
      StorageKeys.savedTextContent: content,
      StorageKeys.savedTextCreatedAt: createdAt.millisecondsSinceEpoch,
      StorageKeys.savedTextUpdatedAt: updatedAt.millisecondsSinceEpoch,
    };
  }
  
  /// Converts the model to a domain entity
  SavedText toEntity() {
    return SavedText(
      id: id,
      title: title,
      content: content,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
  
  /// Creates a model from a domain entity
  factory SavedTextModel.fromEntity(SavedText entity) {
    return SavedTextModel(
      id: entity.id,
      title: entity.title,
      content: entity.content,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
