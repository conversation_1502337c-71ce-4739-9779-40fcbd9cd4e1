import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart' as app_exceptions;
import '../../domain/entities/saved_text.dart';
import '../../domain/repositories/text_repository.dart';
import '../datasources/text_local_datasource.dart';
import '../models/saved_text_model.dart';

class TextRepositoryImpl implements TextRepository {
  final TextLocalDatasource localDatasource;
  
  TextRepositoryImpl(this.localDatasource);
  
  @override
  Future<Either<Failure, SavedText>> addText({
    required String title,
    required String content,
  }) async {
    try {
      final textModel = await localDatasource.addText(
        title: title,
        content: content,
      );
      return Right(textModel.toEntity());
    } on app_exceptions.DatabaseException catch (e) {
      return Left(Failure.database(message: e.message, code: e.code));
    } catch (e) {
      return Left(Failure.unknown(message: 'Failed to add text: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SavedText>>> getSavedTexts() async {
    try {
      final textModels = await localDatasource.getSavedTexts();
      final entities = textModels.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on app_exceptions.DatabaseException catch (e) {
      return Left(Failure.database(message: e.message, code: e.code));
    } catch (e) {
      return Left(Failure.unknown(message: 'Failed to get saved texts: $e'));
    }
  }

  @override
  Future<Either<Failure, SavedText>> getTextById(String id) async {
    try {
      final textModel = await localDatasource.getTextById(id);
      return Right(textModel.toEntity());
    } on app_exceptions.DatabaseException catch (e) {
      return Left(Failure.database(message: e.message, code: e.code));
    } catch (e) {
      return Left(Failure.unknown(message: 'Failed to get text by id: $e'));
    }
  }

  @override
  Future<Either<Failure, SavedText>> updateText(SavedText text) async {
    try {
      final textModel = SavedTextModel.fromEntity(text);
      final updatedModel = await localDatasource.updateText(textModel);
      return Right(updatedModel.toEntity());
    } on app_exceptions.DatabaseException catch (e) {
      return Left(Failure.database(message: e.message, code: e.code));
    } catch (e) {
      return Left(Failure.unknown(message: 'Failed to update text: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteText(String id) async {
    try {
      await localDatasource.deleteText(id);
      return const Right(null);
    } on app_exceptions.DatabaseException catch (e) {
      return Left(Failure.database(message: e.message, code: e.code));
    } catch (e) {
      return Left(Failure.unknown(message: 'Failed to delete text: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SavedText>>> searchTexts(String query) async {
    try {
      final textModels = await localDatasource.searchTexts(query);
      final entities = textModels.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on app_exceptions.DatabaseException catch (e) {
      return Left(Failure.database(message: e.message, code: e.code));
    } catch (e) {
      return Left(Failure.unknown(message: 'Failed to search texts: $e'));
    }
  }
}
