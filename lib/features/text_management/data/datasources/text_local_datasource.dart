import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/storage_keys.dart';
import '../../../../core/errors/exceptions.dart' as app_exceptions;
import '../models/saved_text_model.dart';

abstract class TextLocalDatasource {
  Future<SavedTextModel> addText({
    required String title,
    required String content,
  });
  
  Future<List<SavedTextModel>> getSavedTexts();
  Future<SavedTextModel> getTextById(String id);
  Future<SavedTextModel> updateText(SavedTextModel text);
  Future<void> deleteText(String id);
  Future<List<SavedTextModel>> searchTexts(String query);
}

class TextLocalDatasourceImpl implements TextLocalDatasource {
  final Database database;
  final Uuid _uuid = const Uuid();
  
  TextLocalDatasourceImpl(this.database);
  
  @override
  Future<SavedTextModel> addText({
    required String title,
    required String content,
  }) async {
    try {
      final now = DateTime.now();
      final textModel = SavedTextModel(
        id: _uuid.v4(),
        title: title,
        content: content,
        createdAt: now,
        updatedAt: now,
      );
      
      await database.insert(
        AppConstants.savedTextsTable,
        textModel.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      return textModel;
    } catch (e) {
      throw app_exceptions.DatabaseException('Failed to add text: $e');
    }
  }
  
  @override
  Future<List<SavedTextModel>> getSavedTexts() async {
    try {
      final List<Map<String, dynamic>> maps = await database.query(
        AppConstants.savedTextsTable,
        orderBy: '${StorageKeys.savedTextUpdatedAt} DESC',
      );
      
      return maps.map((map) => SavedTextModel.fromMap(map)).toList();
    } catch (e) {
      throw app_exceptions.DatabaseException('Failed to get saved texts: $e');
    }
  }
  
  @override
  Future<SavedTextModel> getTextById(String id) async {
    try {
      final List<Map<String, dynamic>> maps = await database.query(
        AppConstants.savedTextsTable,
        where: '${StorageKeys.savedTextId} = ?',
        whereArgs: [id],
        limit: 1,
      );
      
      if (maps.isEmpty) {
        throw app_exceptions.DatabaseException('Text with id $id not found');
      }

      return SavedTextModel.fromMap(maps.first);
    } catch (e) {
      throw app_exceptions.DatabaseException('Failed to get text by id: $e');
    }
  }
  
  @override
  Future<SavedTextModel> updateText(SavedTextModel text) async {
    try {
      final updatedText = text.copyWith(updatedAt: DateTime.now());
      
      final count = await database.update(
        AppConstants.savedTextsTable,
        updatedText.toMap(),
        where: '${StorageKeys.savedTextId} = ?',
        whereArgs: [text.id],
      );
      
      if (count == 0) {
        throw app_exceptions.DatabaseException('Text with id ${text.id} not found');
      }

      return updatedText;
    } catch (e) {
      throw app_exceptions.DatabaseException('Failed to update text: $e');
    }
  }
  
  @override
  Future<void> deleteText(String id) async {
    try {
      final count = await database.delete(
        AppConstants.savedTextsTable,
        where: '${StorageKeys.savedTextId} = ?',
        whereArgs: [id],
      );
      
      if (count == 0) {
        throw app_exceptions.DatabaseException('Text with id $id not found');
      }
    } catch (e) {
      throw app_exceptions.DatabaseException('Failed to delete text: $e');
    }
  }
  
  @override
  Future<List<SavedTextModel>> searchTexts(String query) async {
    try {
      final List<Map<String, dynamic>> maps = await database.query(
        AppConstants.savedTextsTable,
        where: '${StorageKeys.savedTextTitle} LIKE ? OR ${StorageKeys.savedTextContent} LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: '${StorageKeys.savedTextUpdatedAt} DESC',
      );
      
      return maps.map((map) => SavedTextModel.fromMap(map)).toList();
    } catch (e) {
      throw app_exceptions.DatabaseException('Failed to search texts: $e');
    }
  }
}
