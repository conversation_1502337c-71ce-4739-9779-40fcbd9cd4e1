import 'package:freezed_annotation/freezed_annotation.dart';

part 'saved_text.freezed.dart';

@freezed
class SavedText with _$SavedText {
  const factory SavedText({
    required String id,
    required String title,
    required String content,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _SavedText;
  
  const SavedText._();
  
  /// Gets the list of words from the content
  List<String> get words {
    return content
        .trim()
        .replaceAll(RegExp(r'\s+'), ' ')
        .split(' ')
        .where((word) => word.isNotEmpty)
        .toList();
  }
  
  /// Gets the word count
  int get wordCount => words.length;
  
  /// Gets a preview of the content (first 100 characters)
  String get preview {
    if (content.length <= 100) return content;
    return '${content.substring(0, 97)}...';
  }
  
  /// Checks if the text is valid (has content and Arabic characters)
  bool get isValid {
    return content.trim().isNotEmpty && 
           RegExp(r'[\u0600-\u06FF]').hasMatch(content);
  }
}
