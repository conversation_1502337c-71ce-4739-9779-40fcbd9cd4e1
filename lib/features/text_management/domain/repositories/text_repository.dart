import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/saved_text.dart';

abstract class TextRepository {
  /// Adds a new text to the repository
  Future<Either<Failure, SavedText>> addText({
    required String title,
    required String content,
  });
  
  /// Gets all saved texts from the repository
  Future<Either<Failure, List<SavedText>>> getSavedTexts();
  
  /// Gets a specific text by its ID
  Future<Either<Failure, SavedText>> getTextById(String id);
  
  /// Updates an existing text
  Future<Either<Failure, SavedText>> updateText(SavedText text);
  
  /// Deletes a text by its ID
  Future<Either<Failure, void>> deleteText(String id);
  
  /// Searches for texts containing the given query
  Future<Either<Failure, List<SavedText>>> searchTexts(String query);
}
