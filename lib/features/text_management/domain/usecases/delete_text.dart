import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../repositories/text_repository.dart';

class DeleteText {
  final TextRepository repository;
  
  DeleteText(this.repository);
  
  Future<Either<Failure, void>> call(String id) async {
    // Validate input
    if (id.trim().isEmpty) {
      return const Left(Failure.textValidation(
        message: 'معرف النص غير صحيح',
      ));
    }
    
    return await repository.deleteText(id);
  }
}
