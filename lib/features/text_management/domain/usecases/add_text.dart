import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/constants/app_constants.dart';
import '../entities/saved_text.dart';
import '../repositories/text_repository.dart';

class AddText {
  final TextRepository repository;
  
  AddText(this.repository);
  
  Future<Either<Failure, SavedText>> call({
    required String title,
    required String content,
  }) async {
    // Validate input
    final validationResult = _validateInput(title, content);
    if (validationResult != null) {
      return Left(validationResult);
    }
    
    // Add text through repository
    return await repository.addText(
      title: title.trim(),
      content: content.trim(),
    );
  }
  
  Failure? _validateInput(String title, String content) {
    // Check if title is empty
    if (title.trim().isEmpty) {
      return const Failure.textValidation(
        message: 'يرجى إدخال عنوان للنص',
      );
    }
    
    // Check if content is empty
    if (content.trim().isEmpty) {
      return const Failure.textValidation(
        message: AppConstants.emptyTextError,
      );
    }
    
    // Check title length
    if (title.trim().length > AppConstants.maxTitleLength) {
      return const Failure.textValidation(
        message: 'العنوان طويل جداً',
      );
    }
    
    // Check content length
    if (content.trim().length > AppConstants.maxTextLength) {
      return const Failure.textValidation(
        message: AppConstants.textTooLongError,
      );
    }
    
    // Check if content contains Arabic characters
    if (!RegExp(r'[\u0600-\u06FF]').hasMatch(content)) {
      return const Failure.textValidation(
        message: 'يجب أن يحتوي النص على أحرف عربية',
      );
    }
    
    return null;
  }
}
