import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/services/dependency_injection.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/saved_text.dart';
import '../../domain/usecases/add_text.dart';
import '../../domain/usecases/get_saved_texts.dart';
import '../../domain/usecases/delete_text.dart';

part 'text_management_controller.freezed.dart';

@freezed
class TextManagementState with _$TextManagementState {
  const factory TextManagementState({
    @Default([]) List<SavedText> savedTexts,
    @Default(false) bool isLoading,
    @Default(false) bool isAdding,
    @Default(false) bool isDeleting,
    String? errorMessage,
    String? successMessage,
  }) = _TextManagementState;
}

class TextManagementController extends StateNotifier<TextManagementState> {
  final AddText _addText;
  final GetSavedTexts _getSavedTexts;
  final DeleteText _deleteText;
  
  TextManagementController({
    required AddText addText,
    required GetSavedTexts getSavedTexts,
    required DeleteText deleteText,
  }) : _addText = addText,
       _getSavedTexts = getSavedTexts,
       _deleteText = deleteText,
       super(const TextManagementState());
  
  /// Loads all saved texts
  Future<void> loadSavedTexts() async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    
    final result = await _getSavedTexts();
    
    result.fold(
      (failure) => state = state.copyWith(
        isLoading: false,
        errorMessage: failure.userMessage,
      ),
      (texts) => state = state.copyWith(
        isLoading: false,
        savedTexts: texts,
        errorMessage: null,
      ),
    );
  }
  
  /// Adds a new text
  Future<bool> addText({
    required String title,
    required String content,
  }) async {
    state = state.copyWith(isAdding: true, errorMessage: null, successMessage: null);
    
    final result = await _addText(title: title, content: content);
    
    return result.fold(
      (failure) {
        state = state.copyWith(
          isAdding: false,
          errorMessage: failure.userMessage,
        );
        return false;
      },
      (savedText) {
        state = state.copyWith(
          isAdding: false,
          savedTexts: [savedText, ...state.savedTexts],
          successMessage: 'تم حفظ النص بنجاح',
          errorMessage: null,
        );
        return true;
      },
    );
  }
  
  /// Deletes a text by ID
  Future<bool> deleteText(String id) async {
    state = state.copyWith(isDeleting: true, errorMessage: null);
    
    final result = await _deleteText(id);
    
    return result.fold(
      (failure) {
        state = state.copyWith(
          isDeleting: false,
          errorMessage: failure.userMessage,
        );
        return false;
      },
      (_) {
        final updatedTexts = state.savedTexts.where((text) => text.id != id).toList();
        state = state.copyWith(
          isDeleting: false,
          savedTexts: updatedTexts,
          successMessage: 'تم حذف النص بنجاح',
          errorMessage: null,
        );
        return true;
      },
    );
  }
  
  /// Clears error and success messages
  void clearMessages() {
    state = state.copyWith(errorMessage: null, successMessage: null);
  }
  
  /// Searches texts by query
  List<SavedText> searchTexts(String query) {
    if (query.trim().isEmpty) {
      return state.savedTexts;
    }
    
    final lowercaseQuery = query.toLowerCase();
    return state.savedTexts.where((text) {
      return text.title.toLowerCase().contains(lowercaseQuery) ||
             text.content.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
}

// Provider for the controller
final textManagementControllerProvider = 
    StateNotifierProvider<TextManagementController, TextManagementState>((ref) {
  return TextManagementController(
    addText: getIt<AddText>(),
    getSavedTexts: getIt<GetSavedTexts>(),
    deleteText: getIt<DeleteText>(),
  );
});
