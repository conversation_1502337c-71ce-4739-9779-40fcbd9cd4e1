import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../app/theme/app_theme.dart';

class TextInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final String? hintText;
  final int? maxLines;
  final int? minLines;
  
  const TextInputWidget({
    super.key,
    required this.controller,
    this.validator,
    this.hintText,
    this.maxLines,
    this.minLines,
  });

  @override
  State<TextInputWidget> createState() => _TextInputWidgetState();
}

class _TextInputWidgetState extends State<TextInputWidget> {
  int _currentLength = 0;
  
  @override
  void initState() {
    super.initState();
    _currentLength = widget.controller.text.length;
    widget.controller.addListener(_updateLength);
  }
  
  @override
  void dispose() {
    widget.controller.removeListener(_updateLength);
    super.dispose();
  }
  
  void _updateLength() {
    setState(() {
      _currentLength = widget.controller.text.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          'النص العربي',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Text Field
        TextFormField(
          controller: widget.controller,
          validator: widget.validator,
          maxLines: widget.maxLines ?? 10,
          minLines: widget.minLines ?? 5,
          maxLength: AppConstants.maxTextLength,
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.right,
          style: AppTheme.arabicTextStyle.copyWith(
            fontSize: 16,
          ),
          decoration: InputDecoration(
            hintText: widget.hintText ?? 'الصق النص العربي هنا...',
            hintStyle: TextStyle(
              color: Colors.grey[500],
              fontSize: 16,
            ),
            alignLabelWithHint: true,
            counterText: '', // Hide default counter
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.primaryColor,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Character count and word count
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _getWordCount(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              '$_currentLength / ${AppConstants.maxTextLength}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: _currentLength > AppConstants.maxTextLength * 0.9
                    ? Colors.red
                    : Colors.grey[600],
              ),
            ),
          ],
        ),
        
        // Tips
        if (_currentLength == 0) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Colors.blue[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'نصيحة: يمكنك نسخ النص من أي مصدر ولصقه هنا',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
  
  String _getWordCount() {
    if (_currentLength == 0) return '0 كلمة';
    
    final words = widget.controller.text
        .trim()
        .replaceAll(RegExp(r'\s+'), ' ')
        .split(' ')
        .where((word) => word.isNotEmpty)
        .length;
    
    return '$words كلمة';
  }
}
