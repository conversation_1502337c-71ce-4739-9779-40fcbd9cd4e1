import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_indicator.dart';
import '../controllers/text_management_controller.dart';
import '../widgets/text_input_widget.dart';

class AddTextPageWidget extends ConsumerStatefulWidget {
  const AddTextPageWidget({super.key});

  @override
  ConsumerState<AddTextPageWidget> createState() => _AddTextPageWidgetState();
}

class _AddTextPageWidgetState extends ConsumerState<AddTextPageWidget> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  
  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(textManagementControllerProvider);
    final controller = ref.read(textManagementControllerProvider.notifier);
    
    // Listen to state changes for navigation
    ref.listen<TextManagementState>(textManagementControllerProvider, (previous, next) {
      if (previous?.isAdding == true && next.isAdding == false) {
        if (next.errorMessage == null && next.successMessage != null) {
          // Success - navigate back
          context.router.maybePop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(next.successMessage!),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
      
      // Show error message
      if (next.errorMessage != null && previous?.errorMessage != next.errorMessage) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
        controller.clearMessages();
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة نص جديد'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.router.maybePop(),
        ),
      ),
      body: LoadingOverlay(
        isLoading: state.isAdding,
        loadingMessage: 'جاري حفظ النص...',
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Instructions
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تعليمات',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• أدخل عنواناً مناسباً للنص\n'
                          '• الصق النص العربي الذي تريد حفظه\n'
                          '• تأكد من أن النص يحتوي على أحرف عربية\n'
                          '• يمكن أن يحتوي النص على علامات التشكيل',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Title Input
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'عنوان النص',
                    hintText: 'أدخل عنواناً مناسباً للنص',
                    prefixIcon: Icon(Icons.title),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال عنوان للنص';
                    }
                    if (value.trim().length > AppConstants.maxTitleLength) {
                      return 'العنوان طويل جداً';
                    }
                    return null;
                  },
                  maxLength: AppConstants.maxTitleLength,
                ),
                
                const SizedBox(height: 16),
                
                // Content Input
                TextInputWidget(
                  controller: _contentController,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return AppConstants.emptyTextError;
                    }
                    if (value.trim().length > AppConstants.maxTextLength) {
                      return AppConstants.textTooLongError;
                    }
                    if (!RegExp(r'[\u0600-\u06FF]').hasMatch(value)) {
                      return 'يجب أن يحتوي النص على أحرف عربية';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 24),
                
                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: CustomButton(
                        text: 'إلغاء',
                        onPressed: () => context.router.maybePop(),
                        isOutlined: true,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CustomButton(
                        text: 'حفظ النص',
                        onPressed: _saveText,
                        isLoading: state.isAdding,
                        icon: Icons.save,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  void _saveText() async {
    if (_formKey.currentState?.validate() ?? false) {
      final controller = ref.read(textManagementControllerProvider.notifier);
      
      await controller.addText(
        title: _titleController.text.trim(),
        content: _contentController.text.trim(),
      );
    }
  }
}
