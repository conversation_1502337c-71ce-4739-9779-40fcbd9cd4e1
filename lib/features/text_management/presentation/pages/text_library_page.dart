import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../app/router/app_router.dart';
import '../../../../shared/widgets/loading_indicator.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../controllers/text_management_controller.dart';
import '../widgets/saved_text_item.dart';
import '../../domain/entities/saved_text.dart';

class TextLibraryPageWidget extends ConsumerStatefulWidget {
  const TextLibraryPageWidget({super.key});

  @override
  ConsumerState<TextLibraryPageWidget> createState() => _TextLibraryPageWidgetState();
}

class _TextLibraryPageWidgetState extends ConsumerState<TextLibraryPageWidget> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load saved texts when the page is first opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(textManagementControllerProvider.notifier).loadSavedTexts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(textManagementControllerProvider);
    final controller = ref.read(textManagementControllerProvider.notifier);
    
    // Filter texts based on search query
    final filteredTexts = _searchQuery.isEmpty 
        ? state.savedTexts 
        : controller.searchTexts(_searchQuery);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مكتبة النصوص'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadSavedTexts(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'البحث في النصوص...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          // Content
          Expanded(
            child: _buildContent(state, filteredTexts, controller),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.router.push(const AddTextRoute()),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent(
    TextManagementState state,
    List<SavedText> filteredTexts,
    TextManagementController controller,
  ) {
    // Show loading indicator
    if (state.isLoading) {
      return const LoadingIndicator(message: 'جاري تحميل النصوص...');
    }

    // Show error message
    if (state.errorMessage != null) {
      return _buildErrorWidget(state.errorMessage!, controller);
    }

    // Show empty state
    if (state.savedTexts.isEmpty) {
      return _buildEmptyState();
    }

    // Show no search results
    if (filteredTexts.isEmpty && _searchQuery.isNotEmpty) {
      return _buildNoSearchResults();
    }

    // Show texts list
    return RefreshIndicator(
      onRefresh: () => controller.loadSavedTexts(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredTexts.length,
        itemBuilder: (context, index) {
          final text = filteredTexts[index];
          return SavedTextItem(
            savedText: text,
            onTap: () => _startMemorizationSession(text),
            onDelete: () => _showDeleteConfirmation(text, controller),
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget(String errorMessage, TextManagementController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              errorMessage,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'إعادة المحاولة',
              onPressed: () => controller.loadSavedTexts(),
              icon: Icons.refresh,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.library_books_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نصوص محفوظة',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'اضغط على زر + لإضافة نص جديد',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'إضافة نص جديد',
              onPressed: () => context.router.push(const AddTextRoute()),
              icon: Icons.add,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoSearchResults() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج للبحث',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'جرب كلمات بحث مختلفة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _startMemorizationSession(SavedText text) {
    context.router.push(MemorizationSessionRoute(savedText: text));
  }

  void _showDeleteConfirmation(SavedText text, TextManagementController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف النص "${text.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await controller.deleteText(text.id);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
