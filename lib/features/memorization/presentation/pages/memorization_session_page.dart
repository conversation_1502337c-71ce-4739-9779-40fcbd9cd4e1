import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../app/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/widgets/loading_indicator.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../text_management/domain/entities/saved_text.dart';
import '../controllers/memorization_controller.dart';
import '../widgets/word_placeholder_widget.dart';
import '../widgets/session_controls.dart';
import '../widgets/completion_dialog.dart';

class MemorizationSessionPageWidget extends ConsumerStatefulWidget {
  final SavedText savedText;

  const MemorizationSessionPageWidget({
    super.key,
    required this.savedText,
  });

  @override
  ConsumerState<MemorizationSessionPageWidget> createState() =>
      _MemorizationSessionPageWidgetState();
}

class _MemorizationSessionPageWidgetState
    extends ConsumerState<MemorizationSessionPageWidget> {

  @override
  void initState() {
    super.initState();
    // Start the memorization session when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(memorizationControllerProvider.notifier)
          .startMemorizationSession(widget.savedText);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(memorizationControllerProvider);
    final controller = ref.read(memorizationControllerProvider.notifier);

    // Listen for session completion
    ref.listen<MemorizationState>(memorizationControllerProvider, (previous, next) {
      if (next.isCompleted && previous?.isCompleted != true) {
        _showCompletionDialog(context);
      }

      // Show error messages
      if (next.hasError && next.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.errorMessage!),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Dismiss',
              onPressed: () => controller.clearError(),
            ),
          ),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.savedText.title),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.router.maybePop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: state.isInitializing ? null : () => controller.resetSession(),
          ),
        ],
      ),
      body: _buildBody(state, controller),
    );
  }

  Widget _buildBody(MemorizationState state, MemorizationController controller) {
    if (state.isInitializing) {
      return const LoadingIndicator(message: 'Initializing session...');
    }

    if (state.session == null) {
      return _buildErrorState(controller);
    }

    return Column(
      children: [
        // Progress indicator
        _buildProgressIndicator(state),

        // Instructions
        _buildInstructions(state),

        // Word placeholders
        Expanded(
          child: _buildWordGrid(state),
        ),

        // Session controls
        SessionControls(
          isListening: state.isListening,
          isCompleted: state.isCompleted,
          onStartListening: () => controller.restartListening(),
          onStopListening: () => controller.restartListening(),
          onReset: () => controller.resetSession(),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(MemorizationState state) {
    final progress = state.currentWordIndex / state.session!.words.length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${state.currentWordIndex} / ${state.session!.words.length} words',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions(MemorizationState state) {
    String instruction;
    Color instructionColor;

    if (state.isCompleted) {
      instruction = AppConstants.congratulationsMessage;
      instructionColor = Colors.green;
    } else if (state.isListening) {
      instruction = AppConstants.listeningMessage;
      instructionColor = Colors.blue;
    } else {
      instruction = AppConstants.sessionStartMessage;
      instructionColor = Colors.grey[600]!;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        instruction,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: instructionColor,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildWordGrid(MemorizationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 12,
        runSpacing: 12,
        children: List.generate(
          state.session!.words.length,
          (index) => WordPlaceholderWidget(
            word: state.session!.words[index],
            revealedWord: state.revealedWords[index],
            isCurrentWord: index == state.currentWordIndex,
            isRevealed: state.revealedWords[index].isNotEmpty,
            showError: state.lastMatchResult?.isMatch == false &&
                      index == state.currentWordIndex,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(MemorizationController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to start memorization session',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Try Again',
              onPressed: () => controller.startMemorizationSession(widget.savedText),
              icon: Icons.refresh,
            ),
          ],
        ),
      ),
    );
  }

  void _showCompletionDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CompletionDialog(
        onRestart: () {
          Navigator.of(context).pop();
          ref.read(memorizationControllerProvider.notifier).resetSession();
        },
        onExit: () {
          Navigator.of(context).pop();
          context.router.maybePop();
        },
      ),
    );
  }
}


