import 'package:flutter/material.dart';
import '../../../../shared/widgets/custom_button.dart';

class SessionControls extends StatelessWidget {
  final bool isListening;
  final bool isCompleted;
  final VoidCallback onStartListening;
  final VoidCallback onStopListening;
  final VoidCallback onReset;
  
  const SessionControls({
    super.key,
    required this.isListening,
    required this.isCompleted,
    required this.onStartListening,
    required this.onStopListening,
    required this.onReset,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Listening status indicator
            _buildStatusIndicator(context),
            
            const SizedBox(height: 16),
            
            // Control buttons
            Row(
              children: [
                // Reset button
                Expanded(
                  child: CustomButton(
                    text: 'Reset',
                    onPressed: onReset,
                    isOutlined: true,
                    icon: Icons.refresh,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Main action button
                Expanded(
                  flex: 2,
                  child: _buildMainActionButton(context),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(BuildContext context) {
    final theme = Theme.of(context);
    
    String statusText;
    Color statusColor;
    IconData statusIcon;
    
    if (isCompleted) {
      statusText = 'Session Completed!';
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    } else if (isListening) {
      statusText = 'Listening...';
      statusColor = Colors.blue;
      statusIcon = Icons.mic;
    } else {
      statusText = 'Ready to listen';
      statusColor = Colors.grey[600]!;
      statusIcon = Icons.mic_off;
    }
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          statusIcon,
          color: statusColor,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          statusText,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: statusColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (isListening) ...[
          const SizedBox(width: 8),
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(statusColor),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMainActionButton(BuildContext context) {
    if (isCompleted) {
      return CustomButton(
        text: 'Start New Session',
        onPressed: onReset,
        icon: Icons.play_arrow,
        backgroundColor: Colors.green,
      );
    }
    
    if (isListening) {
      return CustomButton(
        text: 'Stop Listening',
        onPressed: onStopListening,
        icon: Icons.stop,
        backgroundColor: Colors.red,
      );
    }
    
    return CustomButton(
      text: 'Start Listening',
      onPressed: onStartListening,
      icon: Icons.mic,
    );
  }
}
