import 'package:flutter/material.dart';
import '../../../../app/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class WordPlaceholderWidget extends StatefulWidget {
  final String word;
  final String revealedWord;
  final bool isCurrentWord;
  final bool isRevealed;
  final bool showError;
  
  const WordPlaceholderWidget({
    super.key,
    required this.word,
    required this.revealedWord,
    required this.isCurrentWord,
    required this.isRevealed,
    this.showError = false,
  });

  @override
  State<WordPlaceholderWidget> createState() => _WordPlaceholderWidgetState();
}

class _WordPlaceholderWidgetState extends State<WordPlaceholderWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: Colors.red.withValues(alpha: 0.3),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(WordPlaceholderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Animate when word is revealed
    if (!oldWidget.isRevealed && widget.isRevealed) {
      _animationController.forward().then((_) {
        _animationController.reverse();
      });
    }
    
    // Flash red when there's an error
    if (!oldWidget.showError && widget.showError) {
      _animationController.forward().then((_) {
        _animationController.reverse();
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.showError ? _scaleAnimation.value : 1.0,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: _getBackgroundColor(theme),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _getBorderColor(theme),
                width: widget.isCurrentWord ? 2 : 1,
              ),
              boxShadow: widget.isCurrentWord
                  ? [
                      BoxShadow(
                        color: theme.primaryColor.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Text(
              _getDisplayText(),
              style: _getTextStyle(theme),
              textDirection: TextDirection.rtl,
            ),
          ),
        );
      },
    );
  }

  Color _getBackgroundColor(ThemeData theme) {
    if (widget.showError) {
      return _colorAnimation.value ?? Colors.transparent;
    }
    
    if (widget.isRevealed) {
      return AppTheme.successColor.withValues(alpha: 0.1);
    }
    
    if (widget.isCurrentWord) {
      return theme.primaryColor.withValues(alpha: 0.1);
    }
    
    return Colors.grey[100]!;
  }

  Color _getBorderColor(ThemeData theme) {
    if (widget.showError) {
      return AppTheme.incorrectWordColor;
    }
    
    if (widget.isRevealed) {
      return AppTheme.correctWordColor;
    }
    
    if (widget.isCurrentWord) {
      return theme.primaryColor;
    }
    
    return Colors.grey[300]!;
  }

  String _getDisplayText() {
    if (widget.isRevealed) {
      return widget.revealedWord;
    }
    
    return AppConstants.wordPlaceholder;
  }

  TextStyle _getTextStyle(ThemeData theme) {
    if (widget.isRevealed) {
      return AppTheme.correctWordStyle;
    }
    
    if (widget.isCurrentWord) {
      return AppTheme.arabicTextStyle.copyWith(
        color: theme.primaryColor,
        fontWeight: FontWeight.w600,
      );
    }
    
    return AppTheme.placeholderTextStyle;
  }
}
