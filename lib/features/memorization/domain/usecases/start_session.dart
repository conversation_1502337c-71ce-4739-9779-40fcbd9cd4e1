import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/memorization_session.dart';
import '../repositories/memorization_repository.dart';

class StartSession {
  final MemorizationRepository repository;
  
  StartSession(this.repository);
  
  Future<Either<Failure, MemorizationSession>> call(String textId, List<String> words) async {
    return await repository.startSession(textId, words);
  }
}
