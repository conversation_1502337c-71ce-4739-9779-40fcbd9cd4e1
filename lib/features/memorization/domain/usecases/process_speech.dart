import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/word_match_result.dart';
import '../repositories/memorization_repository.dart';

class ProcessSpeech {
  final MemorizationRepository repository;
  
  ProcessSpeech(this.repository);
  
  Future<Either<Failure, WordMatchResult>> call(String spokenWord, String expectedWord) async {
    return await repository.processSpokenWord(spokenWord, expectedWord);
  }
}
