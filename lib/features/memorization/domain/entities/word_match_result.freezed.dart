// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'word_match_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WordMatchResult {
  bool get isMatch => throw _privateConstructorUsedError;
  String get expectedWord => throw _privateConstructorUsedError;
  String get spokenWord => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Create a copy of WordMatchResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WordMatchResultCopyWith<WordMatchResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WordMatchResultCopyWith<$Res> {
  factory $WordMatchResultCopyWith(
          WordMatchResult value, $Res Function(WordMatchResult) then) =
      _$WordMatchResultCopyWithImpl<$Res, WordMatchResult>;
  @useResult
  $Res call(
      {bool isMatch,
      String expectedWord,
      String spokenWord,
      DateTime timestamp});
}

/// @nodoc
class _$WordMatchResultCopyWithImpl<$Res, $Val extends WordMatchResult>
    implements $WordMatchResultCopyWith<$Res> {
  _$WordMatchResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WordMatchResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMatch = null,
    Object? expectedWord = null,
    Object? spokenWord = null,
    Object? timestamp = null,
  }) {
    return _then(_value.copyWith(
      isMatch: null == isMatch
          ? _value.isMatch
          : isMatch // ignore: cast_nullable_to_non_nullable
              as bool,
      expectedWord: null == expectedWord
          ? _value.expectedWord
          : expectedWord // ignore: cast_nullable_to_non_nullable
              as String,
      spokenWord: null == spokenWord
          ? _value.spokenWord
          : spokenWord // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WordMatchResultImplCopyWith<$Res>
    implements $WordMatchResultCopyWith<$Res> {
  factory _$$WordMatchResultImplCopyWith(_$WordMatchResultImpl value,
          $Res Function(_$WordMatchResultImpl) then) =
      __$$WordMatchResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isMatch,
      String expectedWord,
      String spokenWord,
      DateTime timestamp});
}

/// @nodoc
class __$$WordMatchResultImplCopyWithImpl<$Res>
    extends _$WordMatchResultCopyWithImpl<$Res, _$WordMatchResultImpl>
    implements _$$WordMatchResultImplCopyWith<$Res> {
  __$$WordMatchResultImplCopyWithImpl(
      _$WordMatchResultImpl _value, $Res Function(_$WordMatchResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of WordMatchResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMatch = null,
    Object? expectedWord = null,
    Object? spokenWord = null,
    Object? timestamp = null,
  }) {
    return _then(_$WordMatchResultImpl(
      isMatch: null == isMatch
          ? _value.isMatch
          : isMatch // ignore: cast_nullable_to_non_nullable
              as bool,
      expectedWord: null == expectedWord
          ? _value.expectedWord
          : expectedWord // ignore: cast_nullable_to_non_nullable
              as String,
      spokenWord: null == spokenWord
          ? _value.spokenWord
          : spokenWord // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$WordMatchResultImpl implements _WordMatchResult {
  const _$WordMatchResultImpl(
      {required this.isMatch,
      required this.expectedWord,
      required this.spokenWord,
      required this.timestamp});

  @override
  final bool isMatch;
  @override
  final String expectedWord;
  @override
  final String spokenWord;
  @override
  final DateTime timestamp;

  @override
  String toString() {
    return 'WordMatchResult(isMatch: $isMatch, expectedWord: $expectedWord, spokenWord: $spokenWord, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WordMatchResultImpl &&
            (identical(other.isMatch, isMatch) || other.isMatch == isMatch) &&
            (identical(other.expectedWord, expectedWord) ||
                other.expectedWord == expectedWord) &&
            (identical(other.spokenWord, spokenWord) ||
                other.spokenWord == spokenWord) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isMatch, expectedWord, spokenWord, timestamp);

  /// Create a copy of WordMatchResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WordMatchResultImplCopyWith<_$WordMatchResultImpl> get copyWith =>
      __$$WordMatchResultImplCopyWithImpl<_$WordMatchResultImpl>(
          this, _$identity);
}

abstract class _WordMatchResult implements WordMatchResult {
  const factory _WordMatchResult(
      {required final bool isMatch,
      required final String expectedWord,
      required final String spokenWord,
      required final DateTime timestamp}) = _$WordMatchResultImpl;

  @override
  bool get isMatch;
  @override
  String get expectedWord;
  @override
  String get spokenWord;
  @override
  DateTime get timestamp;

  /// Create a copy of WordMatchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WordMatchResultImplCopyWith<_$WordMatchResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
