import 'package:freezed_annotation/freezed_annotation.dart';

part 'memorization_session.freezed.dart';

@freezed
class MemorizationSession with _$MemorizationSession {
  const factory MemorizationSession({
    required String id,
    required String textId,
    required List<String> words,
    @Default(0) int currentWordIndex,
    @Default([]) List<String> completedWords,
    @Default(false) bool isCompleted,
    required DateTime startedAt,
    DateTime? completedAt,
  }) = _MemorizationSession;
}
