import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/memorization_session.dart';
import '../entities/word_match_result.dart';

abstract class MemorizationRepository {
  Future<Either<Failure, MemorizationSession>> startSession(String textId, List<String> words);
  Future<Either<Failure, WordMatchResult>> processSpokenWord(String spokenWord, String expectedWord);
  Future<Either<Failure, bool>> validateWord(String spokenWord, String expectedWord);
}
