import 'package:get_it/get_it.dart';
import 'package:sqflite/sqflite.dart';
import '../../shared/data/local_database.dart';
import '../../features/text_management/data/datasources/text_local_datasource.dart';
import '../../features/text_management/data/repositories/text_repository_impl.dart';
import '../../features/text_management/domain/repositories/text_repository.dart';
import '../../features/text_management/domain/usecases/add_text.dart';
import '../../features/text_management/domain/usecases/get_saved_texts.dart';
import '../../features/text_management/domain/usecases/delete_text.dart';
import '../../features/memorization/data/datasources/speech_recognition_datasource.dart';
import '../../features/memorization/data/repositories/memorization_repository_impl.dart';
import '../../features/memorization/domain/repositories/memorization_repository.dart';
import '../../features/memorization/domain/usecases/start_session.dart';
import '../../features/memorization/domain/usecases/process_speech.dart';
import '../../features/memorization/domain/usecases/validate_word.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupDependencyInjection() async {
  // Database
  final database = await LocalDatabase.instance.database;
  getIt.registerSingleton<Database>(database);

  // Data Sources
  getIt.registerLazySingleton<TextLocalDatasource>(
    () => TextLocalDatasourceImpl(getIt<Database>()),
  );
  
  getIt.registerLazySingleton<SpeechRecognitionDatasource>(
    () => SpeechRecognitionDatasourceImpl(),
  );

  // Repositories
  getIt.registerLazySingleton<TextRepository>(
    () => TextRepositoryImpl(getIt<TextLocalDatasource>()),
  );
  
  getIt.registerLazySingleton<MemorizationRepository>(
    () => MemorizationRepositoryImpl(getIt<SpeechRecognitionDatasource>()),
  );

  // Use Cases - Text Management
  getIt.registerFactory(() => AddText(getIt<TextRepository>()));
  getIt.registerFactory(() => GetSavedTexts(getIt<TextRepository>()));
  getIt.registerFactory(() => DeleteText(getIt<TextRepository>()));

  // Use Cases - Memorization
  getIt.registerFactory(() => StartSession(getIt<MemorizationRepository>()));
  getIt.registerFactory(() => ProcessSpeech(getIt<MemorizationRepository>()));
  getIt.registerFactory(() => ValidateWord(getIt<MemorizationRepository>()));
}
