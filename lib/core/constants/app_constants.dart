class AppConstants {
  // App Info
  static const String appName = 'Hafiz';
  static const String appVersion = '1.0.0';
  
  // Database
  static const String databaseName = 'hafiz_database.db';
  static const int databaseVersion = 1;
  
  // Tables
  static const String savedTextsTable = 'saved_texts';
  
  // Audio
  static const String successSoundPath = 'assets/audio/success.mp3';
  static const String errorSoundPath = 'assets/audio/error.mp3';
  
  // Speech Recognition
  static const String speechLocale = 'ar-SA'; // Arabic (Saudi Arabia)
  static const Duration speechTimeout = Duration(seconds: 30);
  static const Duration pauseTimeout = Duration(seconds: 3);
  
  // UI
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration flashDuration = Duration(milliseconds: 500);
  
  // Text Processing
  static const String wordPlaceholder = '...';
  static const int maxTextLength = 10000;
  static const int maxTitleLength = 100;
  
  // Session
  static const String congratulationsMessage = 'مبروك! لقد أتممت النص بنجاح';
  static const String sessionStartMessage = 'ابدأ بقراءة النص';
  static const String listeningMessage = 'أستمع إليك...';
  
  // Error Messages
  static const String microphonePermissionError = 'يرجى السماح بالوصول للميكروفون';
  static const String speechRecognitionError = 'خطأ في التعرف على الصوت';
  static const String databaseError = 'خطأ في قاعدة البيانات';
  static const String emptyTextError = 'يرجى إدخال نص';
  static const String textTooLongError = 'النص طويل جداً';
}
