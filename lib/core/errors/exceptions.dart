/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  
  const AppException(this.message, {this.code});
  
  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Database related exceptions
class DatabaseException extends AppException {
  const DatabaseException(super.message, {super.code});
}

/// Speech recognition related exceptions
class SpeechRecognitionException extends AppException {
  const SpeechRecognitionException(super.message, {super.code});
}

/// Permission related exceptions
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code});
}

/// Audio playback related exceptions
class AudioException extends AppException {
  const AudioException(super.message, {super.code});
}

/// Text validation related exceptions
class TextValidationException extends AppException {
  const TextValidationException(super.message, {super.code});
}

/// Network related exceptions (for future use)
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code});
}

/// Cache related exceptions
class CacheException extends AppException {
  const CacheException(super.message, {super.code});
}
