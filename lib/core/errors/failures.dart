import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// Base failure class for handling errors in the application
@freezed
class Failure with _$Failure {
  const factory Failure.database({
    required String message,
    String? code,
  }) = DatabaseFailure;
  
  const factory Failure.speechRecognition({
    required String message,
    String? code,
  }) = SpeechRecognitionFailure;
  
  const factory Failure.permission({
    required String message,
    String? code,
  }) = PermissionFailure;
  
  const factory Failure.audio({
    required String message,
    String? code,
  }) = AudioFailure;
  
  const factory Failure.textValidation({
    required String message,
    String? code,
  }) = TextValidationFailure;
  
  const factory Failure.network({
    required String message,
    String? code,
  }) = NetworkFailure;
  
  const factory Failure.cache({
    required String message,
    String? code,
  }) = CacheFailure;
  
  const factory Failure.unknown({
    required String message,
    String? code,
  }) = UnknownFailure;
}

/// Extension to get user-friendly error messages
extension FailureExtension on Failure {
  String get userMessage {
    return when(
      database: (message, code) => 'حدث خطأ في قاعدة البيانات',
      speechRecognition: (message, code) => 'خطأ في التعرف على الصوت',
      permission: (message, code) => 'يرجى السماح بالصلاحيات المطلوبة',
      audio: (message, code) => 'خطأ في تشغيل الصوت',
      textValidation: (message, code) => message,
      network: (message, code) => 'خطأ في الاتصال بالإنترنت',
      cache: (message, code) => 'خطأ في التخزين المؤقت',
      unknown: (message, code) => 'حدث خطأ غير متوقع',
    );
  }
}
