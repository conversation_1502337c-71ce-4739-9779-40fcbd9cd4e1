import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../core/constants/app_constants.dart';
import '../../core/constants/storage_keys.dart';

class LocalDatabase {
  static final LocalDatabase _instance = LocalDatabase._internal();
  static Database? _database;

  LocalDatabase._internal();

  static LocalDatabase get instance => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await _createSavedTextsTable(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add upgrade logic for version 2
    }
  }

  Future<void> _createSavedTextsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${AppConstants.savedTextsTable} (
        ${StorageKeys.savedTextId} TEXT PRIMARY KEY,
        ${StorageKeys.savedTextTitle} TEXT NOT NULL,
        ${StorageKeys.savedTextContent} TEXT NOT NULL,
        ${StorageKeys.savedTextCreatedAt} INTEGER NOT NULL,
        ${StorageKeys.savedTextUpdatedAt} INTEGER NOT NULL
      )
    ''');
  }

  Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  Future<void> deleteDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
