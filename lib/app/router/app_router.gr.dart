// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

abstract class _$AppRouter extends RootStackRouter {
  // ignore: unused_element
  _$AppRouter({super.navigatorKey});

  @override
  final Map<String, PageFactory> pagesMap = {
    AddTextRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const AddTextPage(),
      );
    },
    MemorizationSessionRoute.name: (routeData) {
      final args = routeData.argsAs<MemorizationSessionRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: MemorizationSessionPage(
          key: args.key,
          savedText: args.savedText,
        ),
      );
    },
    TextLibraryRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const TextLibraryPage(),
      );
    },
  };
}

/// generated route for
/// [AddTextPage]
class AddTextRoute extends PageRouteInfo<void> {
  const AddTextRoute({List<PageRouteInfo>? children})
      : super(
          AddTextRoute.name,
          initialChildren: children,
        );

  static const String name = 'AddTextRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [MemorizationSessionPage]
class MemorizationSessionRoute
    extends PageRouteInfo<MemorizationSessionRouteArgs> {
  MemorizationSessionRoute({
    Key? key,
    required SavedText savedText,
    List<PageRouteInfo>? children,
  }) : super(
          MemorizationSessionRoute.name,
          args: MemorizationSessionRouteArgs(
            key: key,
            savedText: savedText,
          ),
          initialChildren: children,
        );

  static const String name = 'MemorizationSessionRoute';

  static const PageInfo<MemorizationSessionRouteArgs> page =
      PageInfo<MemorizationSessionRouteArgs>(name);
}

class MemorizationSessionRouteArgs {
  const MemorizationSessionRouteArgs({
    this.key,
    required this.savedText,
  });

  final Key? key;

  final SavedText savedText;

  @override
  String toString() {
    return 'MemorizationSessionRouteArgs{key: $key, savedText: $savedText}';
  }
}

/// generated route for
/// [TextLibraryPage]
class TextLibraryRoute extends PageRouteInfo<void> {
  const TextLibraryRoute({List<PageRouteInfo>? children})
      : super(
          TextLibraryRoute.name,
          initialChildren: children,
        );

  static const String name = 'TextLibraryRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}
